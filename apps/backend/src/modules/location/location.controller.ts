import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { LocationService } from './location.service';

@ApiTags('Locations')
@Controller('locations')
export class LocationController {
  constructor(private readonly locationService: LocationService) {}

  @Get('countries')
  @ApiOperation({ summary: 'Get all countries' })
  @ApiResponse({ status: 200, description: 'Countries retrieved successfully' })
  async getCountries() {
    return this.locationService.getCountries();
  }

  @Get('countries/:countryId/states')
  @ApiOperation({ summary: 'Get states by country' })
  @ApiResponse({ status: 200, description: 'States retrieved successfully' })
  async getStatesByCountry(@Param('countryId') countryId: string) {
    return this.locationService.getStatesByCountry(countryId);
  }

  @Get('states/:stateId/cities')
  @ApiOperation({ summary: 'Get cities by state' })
  @ApiResponse({ status: 200, description: 'Cities retrieved successfully' })
  async getCitiesByState(@Param('stateId') stateId: string) {
    return this.locationService.getCitiesByState(stateId);
  }
}
