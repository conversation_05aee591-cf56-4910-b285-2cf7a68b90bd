import { Injectable } from '@nestjs/common';
import { Country, State, City } from '@can-sell/types';

@Injectable()
export class LocationService {
  // TODO: Replace with actual database operations
  async getCountries(): Promise<Country[]> {
    return [
      {
        id: '1',
        name: 'United States',
        code: 'US',
        states: [],
      },
      {
        id: '2',
        name: 'Canada',
        code: 'CA',
        states: [],
      },
    ];
  }

  async getStatesByCountry(countryId: string): Promise<State[]> {
    // Mock data
    if (countryId === '1') {
      return [
        {
          id: '1',
          name: 'California',
          code: 'CA',
          countryId: '1',
          cities: [],
        },
        {
          id: '2',
          name: 'New York',
          code: 'NY',
          countryId: '1',
          cities: [],
        },
      ];
    }
    return [];
  }

  async getCitiesByState(stateId: string): Promise<City[]> {
    // Mock data
    if (stateId === '1') {
      return [
        {
          id: '1',
          name: 'Los Angeles',
          stateId: '1',
          latitude: 34.0522,
          longitude: -118.2437,
        },
        {
          id: '2',
          name: 'San Francisco',
          stateId: '1',
          latitude: 37.7749,
          longitude: -122.4194,
        },
      ];
    }
    return [];
  }
}
