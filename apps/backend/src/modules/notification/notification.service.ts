import { Injectable } from '@nestjs/common';
import { Notification } from '@can-sell/types';

@Injectable()
export class NotificationService {
  async getUserNotifications(userId: string): Promise<Notification[]> {
    // TODO: Implement actual notification retrieval
    return [];
  }

  async sendNotification(notification: Partial<Notification>): Promise<void> {
    // TODO: Implement notification sending logic
    console.log('Sending notification:', notification);
  }
}
