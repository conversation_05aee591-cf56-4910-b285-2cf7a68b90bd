import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SearchService } from './search.service';

@ApiTags('Search')
@Controller('search')
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @Get()
  @ApiOperation({ summary: 'Search items' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async searchItems(
    @Query('q') query?: string,
    @Query('category') category?: string,
    @Query('location') location?: string,
    @Query('minPrice') minPrice?: number,
    @Query('maxPrice') maxPrice?: number,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
  ) {
    return this.searchService.searchItems({
      q: query,
      categoryId: category,
      locationId: location,
      minPrice,
      maxPrice,
      page,
      limit,
    });
  }
}
