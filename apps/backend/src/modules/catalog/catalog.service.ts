import { Injectable, NotFoundException } from '@nestjs/common';
import { Category, Template, FieldType } from '@can-sell/types';

@Injectable()
export class CatalogService {
  // TODO: Replace with actual database operations
  private readonly mockCategories: Category[] = [
    {
      id: '1',
      name: 'Electronics',
      slug: 'electronics',
      description: 'Electronic devices and gadgets',
      parentId: null,
      templateIds: ['1', '2'],
      isActive: true,
      sortOrder: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Mobile phones and accessories',
      parentId: '1',
      templateIds: ['1'],
      isActive: true,
      sortOrder: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      name: 'Vehicles',
      slug: 'vehicles',
      description: 'Cars, motorcycles, and other vehicles',
      parentId: null,
      templateIds: ['3'],
      isActive: true,
      sortOrder: 2,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  private readonly mockTemplates: Template[] = [
    {
      id: '1',
      name: 'Smartphone Template',
      description: 'Template for listing smartphones',
      categoryId: '2',
      fields: [
        {
          id: '1',
          name: 'brand',
          label: 'Brand',
          type: FieldType.SELECT,
          required: true,
          options: ['Apple', 'Samsung', 'Google', 'OnePlus', 'Other'],
          sortOrder: 1,
        },
        {
          id: '2',
          name: 'model',
          label: 'Model',
          type: FieldType.TEXT,
          required: true,
          placeholder: 'e.g., iPhone 15 Pro',
          sortOrder: 2,
        },
        {
          id: '3',
          name: 'storage',
          label: 'Storage Capacity',
          type: FieldType.SELECT,
          required: true,
          options: ['64GB', '128GB', '256GB', '512GB', '1TB'],
          sortOrder: 3,
        },
        {
          id: '4',
          name: 'color',
          label: 'Color',
          type: FieldType.TEXT,
          required: false,
          placeholder: 'e.g., Space Gray',
          sortOrder: 4,
        },
      ],
      isActive: true,
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async getCategories(parentId?: string): Promise<Category[]> {
    if (parentId) {
      return this.mockCategories.filter(cat => cat.parentId === parentId);
    }
    return this.mockCategories.filter(cat => cat.parentId === null);
  }

  async getCategoryById(id: string): Promise<Category> {
    const category = this.mockCategories.find(cat => cat.id === id);
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  async getTemplatesByCategory(categoryId: string): Promise<Template[]> {
    const category = await this.getCategoryById(categoryId);
    return this.mockTemplates.filter(template => 
      category.templateIds.includes(template.id)
    );
  }

  async getTemplateById(id: string): Promise<Template> {
    const template = this.mockTemplates.find(t => t.id === id);
    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
    return template;
  }
}
